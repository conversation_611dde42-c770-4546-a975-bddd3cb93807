<div align="center">
  <img src="/non_src/assets/starting_video.gif" alt="Demo">
  <br>
  <img src="/non_src/assets/logo_wide_3.jpg" alt="Logo">
  <br> 
  <h2>Probably the most intelligent AI coder out there.</h2>
  <br>
  Clean Coder is your 2-in-1 AI Scrum Master and Developer. Equipped with most intelligent reasoning system and most andvanced codebase research tool, it makes your code written with absolute minimum time investment on your part!
  <br>
  <br>
    <h3>⭐️ Your star motivates us to introduce new cool features! ⭐️</h3>  
  <br>

[![Docs](https://img.shields.io/badge/docs-latest-blue)](https://clean-coder.dev)
[![Discord](https://img.shields.io/static/v1?logo=discord&label=discord&message=Join&color=brightgreen)](https://discord.gg/8gat7Pv7QJ)

  <a href="https://cleancoder.byst.re/Agent_nr.6-Trailer.mp4" target="_blank" title="Trailer">
    <img src="/non_src/assets/Miniature_trailer.jpg" width="600" alt="Trailer">
  </a>
  <br>

  <img src="/non_src/assets/CC_diagram_light_golden.png">
</div>

## 🏖️ Relax and watch it code

```
git clone https://github.com/GregorD1A1/Clean-Coder-AI
cd Clean-Coder-AI

pip install -r requirements.txt

python manager.py
```
or check detailed instructions [how to start in documentation](https://clean-coder.dev/getting_started/quick_start/).

You can also [deploy with Docker](https://clean-coder.dev/getting_started/run_with_docker/).


## 📺 Demo videos

Create an entire web app ~~with~~ by Clean Coder:

<div align="center">
<a href="https://youtu.be/aNpB-Tw-YPw" title="Greg's Tech video">
  <img src="https://img.youtube.com/vi/aNpB-Tw-YPw/maxresdefault.jpg" width="600" alt="Demo video">
</a>
</div>


## 📊 Why Clean Coder?

Our dream is to create a fully autonomous programmer one day, letting you relax (or at least do another job 😜) while your coding job will be done by AI.

That's why we care about making Clean Coder have top intelligence level and be equipped with most modern automation tools.

Learn more about Clean Coder's idea [here](https://clean-coder.dev/faq/why_clean_coder/).

| Feature | Clean Coder | Cline | Aider | Cursor |
|---------|-------------|--------|-------|---------|
| **Intelligence** | ✅ Two-step Planer agent for thinking only  | 🟡 One-step plan mode | 🟡 One-step Architect agent | ❌ No thinking agent |
| **Codebase Research** | ✅ File descriptions RAG, codebase size independent | ❌ Simple file browsing approach only | 🟡 Repo map | 🟡 Also RAG, but not describes code before indexing  |
| **Project Management** | ✅ Full Todoist integration | ❌ No | ❌ No | ❌ No |
| **Frontend Visual testing** | ✅ Frontend Feedback agent | ✅ Webview | ❌ No | ❌ No |
| **UI** | ❌ Terminal only | ✅ IDE | ✅ Webchat | ✅ IDE |


## ✨ Key advantages:

- Get project supervised by [Manager agent](https://clean-coder.dev/usage/manager/) with tasks organized in Todoist, just like with a human scrum master.
- Two-step planning module makes Clean Coder probably most intelligent [AI coder](https://clean-coder.dev/usage/programmer_pipeline/) available.
- [Semantic search (RAG)](https://clean-coder.dev/advanced_features_installation/similarity_search_for_researcher/) for effective navigating even large codebases.
- Allow AI to see frontend it creates with [frontend feedback feature](https://clean-coder.dev/features/frontend_feedback/). At the day of writing no other AI coder has that feature.
- Create a [frontend based on images](https://clean-coder.dev/features/working_with_images/) with designs.
- [Speak to Clean Coder](https://clean-coder.dev/features/talk_to_cc/) instead of writing.
- Automatic file linting prevents from introducing incorrect changes and [log check for self-debug](https://clean-coder.dev/advanced_features_installation/logs_check/).
- [Run Python scripts](https://clean-coder.dev/advanced_features_installation/python_scripts_run/) for self-debug.


## ⛓️‍💥 Something got broken?

Report bugs or propose new features for Clean Coder on our [Discord](https://discord.gg/8gat7Pv7QJ)!


## 🌟 Say thanks

We hardly work on developing cutting-edge AI coder for you. Completely for free. Support our work by leaving a star!

![Starring](/non_src/assets/star.gif)

## 🎖️ Hall of Fame
<br>
<div align="center">
  <a href="https://github.com/Grigorij-Dudnik/Clean-Coder-AI/graphs/contributors">
    <img src="https://contrib.rocks/image?repo=Grigorij-Dudnik/Clean-Coder-AI&1" />
  </a>
</div>
<br>

Sweat, tears and endless glory... [Join the Clean Coder contributors](https://clean-coder.dev/community/contributions_guide/)!