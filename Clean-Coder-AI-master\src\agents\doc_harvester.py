"""Documentation harvester pulls relevant documentation for the task by user of the pipeline."""

from typing import Union


class Doc_harvester:
    def __init__(self) -> None:
        """Initial information to help harvest documentation from the internet."""
        pass

    def find_documentation(self, task: str, work_dir: str) -> Union[None, list[str]]:
        """Returns documentation relevant for the task set by human user."""
        return None
