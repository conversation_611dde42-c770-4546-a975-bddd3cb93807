# Must have
## Full path of the project dir you work on. Here Clean Coder will write code. It's his workplace, should be outside of Clean Coder dir itself.
WORK_DIR=

# Model providers (provide at least one)
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
OPENROUTER_API_KEY=
OLLAMA_MODEL=
LOCAL_MODEL_API_BASE=
LOCAL_MODEL_NAME=

## For Manager agent
TODOIST_API_KEY=
TODOIST_PROJECT_ID=

# Optional
## For automatic error check
LOG_FILE=
## Frontend Feedback
FRONTEND_URL=
## Edit transcription of voice record before sending to agent
EDIT_TRANSCRIPTION=
## Show planner intermediate reasoning
SHOW_LOGIC_PLAN=
# If the clean-coder should run the generated code.
EXECUTE_FILE_NAME=

